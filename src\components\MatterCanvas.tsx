import { FunctionalComponent } from 'preact';
import { useState } from 'preact/hooks';
import { DocumentTextIcon, ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';

interface MatterCanvasProps {
  service: string;
  matterSummary: string;
  qualityScore: any;
  answers: Record<string, string | { question: string; answer: string }>;
  isExpanded?: boolean;
}

/**
 * Simple markdown-based matter canvas similar to ChatGPT's canvas
 * Just expandable markdown content integrated into chat flow
 */
const MatterCanvas: FunctionalComponent<MatterCanvasProps> = ({
  service,
  matterSummary,
  qualityScore,
  answers,
  isExpanded = false
}) => {
  const [expanded, setExpanded] = useState(isExpanded);

  // Generate markdown content for the matter - CLIENT-FACING ONLY
  const generateMarkdown = () => {
    // Only show the matter summary that was generated by AI for the client
    // No internal metrics, scores, or assessments
    return matterSummary;
  };

  const markdownContent = generateMarkdown();
  const previewContent = markdownContent.split('\n').slice(0, 6).join('\n') + '\n\n*[Click to expand full matter details]*';

  return (
    <div class="matter-canvas">
      <div class="matter-canvas-header">
        <div class="matter-canvas-title">
          <DocumentTextIcon className="w-4 h-4" />
          Matter Canvas
        </div>
        <button 
          class="matter-canvas-toggle" 
          onClick={() => setExpanded(!expanded)}
          aria-label={expanded ? 'Collapse matter details' : 'Expand matter details'}
        >
          {expanded ? (
            <ChevronUpIcon className="w-4 h-4" />
          ) : (
            <ChevronDownIcon className="w-4 h-4" />
          )}
        </button>
      </div>
      
      <div class="matter-canvas-content">
        <pre class="matter-canvas-markdown">
          {expanded ? markdownContent : previewContent}
        </pre>
      </div>
    </div>
  );
};

export default MatterCanvas; 