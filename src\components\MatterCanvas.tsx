import { FunctionalComponent } from 'preact';
import { useState } from 'preact/hooks';

interface MatterCanvasProps {
  service: string;
  matterSummary: string;
  qualityScore: any;
  answers: Record<string, string | { question: string; answer: string }>;
  isExpanded?: boolean;
}

/**
 * Simple markdown-based matter canvas similar to ChatGPT's canvas
 * Just expandable markdown content integrated into chat flow
 */
const MatterCanvas: FunctionalComponent<MatterCanvasProps> = ({
  service,
  matterSummary,
  qualityScore,
  answers,
  isExpanded = false
}) => {
  const [expanded, setExpanded] = useState(isExpanded);

  // Generate markdown content for the matter - CLIENT-FACING ONLY
  const generateMarkdown = () => {
    // Only show the matter summary that was generated by AI for the client
    // No internal metrics, scores, or assessments
    return matterSummary;
  };

  const markdownContent = generateMarkdown();
  const previewContent = markdownContent.split('\n').slice(0, 6).join('\n') + '\n\n*[Click to expand full matter details]*';

  return (
    <div class="matter-canvas">
      <div class="matter-canvas-header">
        <div class="matter-canvas-title">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14,2 14,8 20,8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10,9 9,9 8,9"></polyline>
          </svg>
          Matter Canvas
        </div>
        <button 
          class="matter-canvas-toggle" 
          onClick={() => setExpanded(!expanded)}
          aria-label={expanded ? 'Collapse matter details' : 'Expand matter details'}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points={expanded ? "18 15 12 9 6 15" : "6 9 12 15 18 9"}></polyline>
          </svg>
        </button>
      </div>
      
      <div class="matter-canvas-content">
        <pre class="matter-canvas-markdown">
          {expanded ? markdownContent : previewContent}
        </pre>
      </div>
    </div>
  );
};

export default MatterCanvas; 