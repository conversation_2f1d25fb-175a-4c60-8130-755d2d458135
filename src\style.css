* {
	box-sizing: border-box;
}

/* Heroicons compatibility - scoped sizing utilities for SVG icons only */
svg[class*="w-4"] { width: 1rem; }
svg[class*="h-4"] { height: 1rem; }
svg[class*="w-5"] { width: 1.25rem; }
svg[class*="h-5"] { height: 1.25rem; }
svg[class*="w-6"] { width: 1.5rem; }
svg[class*="h-6"] { height: 1.5rem; }
svg[class*="w-8"] { width: 2rem; }
svg[class*="h-8"] { height: 2rem; }
svg[class*="w-12"] { width: 3rem; }
svg[class*="h-12"] { height: 3rem; }

:root {
	font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
	line-height: 1.5;
	font-weight: 400;
	color-scheme: light dark;

	/* Light theme colors */
	--bg-color: #ffffff;
	--bg-color-rgb: 255, 255, 255;
	--text-color: #1a1a1a;
	--message-bg-user: #f0f0f0;
	--message-bg-ai: #ffffff;
	--border-color: #e5e5e5;
	--input-bg: #ffffff;
	--hover-color: #f5f5f5;
	--accent-color: #666666;
	--skeleton-start: rgba(200, 200, 200, 0.1);
	--skeleton-end: rgba(200, 200, 200, 0.3);
}

@media (prefers-color-scheme: dark) {
	:root {
		/* Dark theme colors */
		--bg-color: #030712;
		--bg-color-rgb: 3, 7, 18;
		--text-color: #ffffff;
		--message-bg-user: #111827;
		--message-bg-ai: #0a0f1a;
		--border-color: #10131a;
		--input-bg: #0a0f1a;
		--hover-color: #111827;
		--accent-color: #64748b;
		--skeleton-start: rgba(10, 15, 26, 0.1);
		--skeleton-end: rgba(10, 15, 26, 0.3);
	}

	.disclaimer-text {
		background-color: var(--bg-color);
		border-top-color: var(--border-color);
	}
}

* {
	margin: 0;
	padding: 0;
	scrollbar-width: thin;
	scrollbar-color: rgba(var(--accent-color-rgb), 0.5) transparent;
}

body {
	background-color: var(--bg-color);
	color: var(--text-color);
	min-height: 100vh;
	margin: 0;
}

#app {
	height: 100vh;
	width: 100vw;
	max-width: none;
	margin: 0;
}

.chat-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	max-width: 1200px;
	margin: 0 auto;
	padding: 0;
	position: relative;
	-webkit-overflow-scrolling: touch;
	scroll-behavior: smooth;
	transition: all 0.3s ease-in-out;
	overflow: hidden;
	/* Prevent body scroll when input is focused */
}

/* Inline mode styling - always full width and height, no animations */
.chat-container.inline {
	position: relative;
	height: 100vh;
	width: 100%;
	max-width: 100%;
	margin: 0;
	overflow: hidden;
	background-color: var(--bg-color);
	box-shadow: none;
	border: none;
	transition: none;
	/* Disable all transitions for inline mode */
	animation: none;
	/* Disable any animations */
	/* Ensure immediate rendering without any animation delay */
	opacity: 1;
	transform: none;
}

/* Ensure input area works properly in inline mode */
.chat-container.inline .input-area {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1001;
}

/* Widget positioning */
.chat-container.widget {
	position: fixed;
	height: calc(100% - 100px);
	/* Leave space for button with some margin */
	width: 400px;
	max-width: 100%;
	margin: 0;
	border-radius: 12px 0 0 0;
	/* Rounded top-left corner for style */
	overflow: hidden;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
	bottom: 90px;
	/* Increased space for button */
	top: auto;
	/* Allow height to be controlled by bottom and height */
	right: 0;
	z-index: 1000;
	background-color: var(--bg-color);
	border-left: 1px solid var(--border-color);
	transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Open/closed states */
.chat-container.open {
	transform: translateX(0);
	opacity: 1;
}

.chat-container.closed {
	transform: translateY(20px);
	opacity: 0;
	pointer-events: none;
}

.chat-container.widget.closed {
	transform: translateY(100%);
	opacity: 0;
	pointer-events: none;
}

/* Toggle button visibility */
.chat-toggle {
	position: absolute;
	top: 20px;
	width: 50px;
	height: 50px;
	border-radius: 50%;
	background-color: var(--accent-color);
	color: var(--bg-color);
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	z-index: 10;
	transition: all 0.2s ease;
	padding: 0;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
	overflow: hidden;
}

/* Button positioning for different states */
.chat-container.widget .chat-toggle {
	display: none;
}

/* Override for the closed state to keep button visible */
.chat-container.widget.closed .chat-toggle {
	display: none;
}

.chat-toggle:hover {
	transform: scale(1.05);
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.chat-toggle svg {
	width: 30px;
	height: 30px;
}

/* Responsive adjustments for widget mode */
@media (max-width: 768px) {
	.chat-container.widget {
		width: 100%;
		height: calc(100% - 100px);
		bottom: 90px;
		top: auto;
		left: 0;
		right: 0;
		border-radius: 20px 20px 0 0;
		/* Rounded top corners on mobile */
	}

	.chat-container.widget.closed {
		transform: translateY(100%);
	}

	/* Remove these rules as they're no longer needed */
	.chat-container.widget.closed .chat-toggle {
		display: none;
	}

	/* Remove the after pseudo-element */
	.chat-container.widget.closed::after {
		display: none;
	}

	/* Remove the before pseudo-element */
	.chat-container.widget.closed::before {
		display: none;
	}

	/* Mobile adjustments for sticky input */
	.input-area {
		padding-bottom: 1rem;
		padding-left: 0.75rem;
		padding-right: 0.75rem;
		bottom: 28px;
		/* Slightly less space on mobile */
	}

	.message-list {
		padding-bottom: 140px;
		/* Account for input area and disclaimer on mobile */
	}

	.disclaimer-text {
		font-size: 0.7rem;
		padding: 0.4rem 0.75rem;
		height: 28px;
		/* Slightly smaller height on mobile */
	}
}

.drag-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	background: radial-gradient(circle, rgba(var(--bg-color-rgb), 0.85) 0%, rgba(var(--bg-color-rgb), 0.95) 100%);
	backdrop-filter: blur(3px);
	animation: fadeIn 0.2s ease-out;
}

.drag-message {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 0.75rem;
	color: var(--text-color);
	font-size: 1.2rem;
	text-align: center;
	padding: 2.5rem 3rem;
	border-radius: 1rem;
	background-color: rgba(var(--bg-color-rgb), 0.8);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
	border: 1px solid var(--border-color);
	max-width: 90%;
	animation: dropMessageAppear 0.4s ease-out;
	position: relative;
	z-index: 10000;
}

.drag-message-icon {
	width: 58px;
	height: 58px;
	color: var(--accent-color);
	margin-bottom: 0.25rem;
}

.drag-message-title {
	font-size: 1.4rem;
	font-weight: 600;
	margin: 0;
	color: var(--text-color);
}

.drag-message-subtitle {
	font-size: 0.9rem;
	opacity: 0.8;
	margin: 0;
	color: var(--accent-color);
}

@keyframes dropMessageAppear {
	0% {
		opacity: 0;
		transform: translateY(10px) scale(0.95);
	}

	100% {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

@keyframes dropIconBounce {

	0%,
	100% {
		transform: translateY(-4px);
	}

	50% {
		transform: translateY(4px);
	}
}

.chat-main {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	overflow: hidden;
	position: relative;
	background-color: var(--bg-color);
}

.message-list {
	flex: 1;
	overflow-y: auto;
	padding: 1rem;
	padding-bottom: 160px;
	/* Add padding to account for sticky input area and disclaimer */
	scroll-behavior: smooth;
	width: 100%;
	scrollbar-width: thin;
	scrollbar-color: transparent transparent;
	transition: scrollbar-color 0.5s ease;
	-webkit-overflow-scrolling: touch;
}

.message-list:hover,
.message-list:focus,
.message-list:active {
	scrollbar-color: var(--accent-color) transparent;
}

/* For WebKit browsers to hide and show scrollbar with animation */
.message-list::-webkit-scrollbar-thumb {
	background-color: transparent;
	transition: background-color 0.5s ease;
}

.message-list:hover::-webkit-scrollbar-thumb,
.message-list:focus::-webkit-scrollbar-thumb,
.message-list:active::-webkit-scrollbar-thumb {
	background-color: var(--accent-color);
}

/* iOS-like auto-hiding scrollbar behavior */
.message-list {
	-webkit-overflow-scrolling: touch;
}

.input-area {
	padding-bottom: 2rem;
	padding-left: 1rem;
	padding-right: 1rem;
	background-color: var(--bg-color);
	height: auto;
	display: flex;
	flex-direction: column;
	width: 100%;
	position: fixed;
	bottom: 32px;
	/* Move input area up to make room for disclaimer */
	left: 0;
	right: 0;
	z-index: 1000;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
}

.disclaimer-text {
	font-size: 0.75rem;
	color: var(--accent-color);
	text-align: center;
	padding: 0.5rem 1rem;
	background-color: var(--bg-color);
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1001;
	/* Higher z-index than input area */
	opacity: 0.8;
	height: 32px;
	/* Fixed height for the disclaimer */
	display: flex;
	align-items: center;
	justify-content: center;
}

.input-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	position: relative;
	background-color: var(--input-bg);
	border: 1px solid var(--border-color);
	border-radius: 1rem;
	padding: 0.75rem;
	min-height: 56px;
	gap: 0.75rem;
	height: auto;
	overflow: visible;
}

.input-preview {
	display: flex;
	flex-wrap: wrap;
	gap: 0.5rem;
	margin: 0;
}

.input-preview-item {
	position: relative;
	width: 60px;
	height: 60px;
	border-radius: 0.5rem;
	overflow: hidden;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	background-color: var(--hover-color);
}

.input-preview-item.image-preview {
	width: 60px;
	height: 60px;
}

.input-preview-item.file-preview {
	width: 165px;
	height: 60px;
	display: flex;
	align-items: center;
	padding-right: 20px;
	gap: 0.25rem;
}

.input-preview-item img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.input-preview-item .file-thumbnail {
	width: 60px;
	height: 60px;
	flex-shrink: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: var(--accent-color);
	color: var(--bg-color);
	padding: 0.5rem;
}

.input-preview-item .file-thumbnail svg {
	width: 32px;
	height: 32px;
}

.input-preview-item .file-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	padding: 0.25rem;
	padding-right: 0.5rem;
	overflow: hidden;
}

.input-preview-item .file-name {
	font-size: 0.75rem;
	font-weight: 500;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 0.25rem;
}

.input-preview-item .file-ext {
	font-size: 0.6rem;
	color: var(--accent-color);
	text-transform: uppermatter;
	font-weight: 600;
}

.input-preview-remove {
	position: absolute;
	top: 4px;
	right: 4px;
	width: 18px;
	height: 18px;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.6);
	color: white;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 16px;
	line-height: 1;
	padding: 0;
	opacity: 0.9;
	transition: opacity 0.2s;
	z-index: 2;
}

.input-preview-remove svg {
	width: 14px;
	height: 14px;
}

.input-preview-remove:hover {
	opacity: 1;
}

.input-controls-row {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	width: 100%;
	padding: 0.0rem;
}

.input-controls {
	display: flex;
	justify-content: space-between;
	width: 100%;
	align-items: center;
}

/* Left side controls */
.input-left-controls {
	display: flex;
	align-items: center;
}

.file-menu-trigger {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background-color: transparent;
	border: 1px solid var(--accent-color);
	cursor: pointer;
	color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.25rem;
}

.file-menu-trigger:hover {
	background-color: var(--hover-color);
}

.file-menu-icon {
	width: 16px;
	height: 16px;
}

.media-icon {
	width: 16px;
	height: 16px;
}

.media-button {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background-color: transparent;
	border: 1px solid var(--accent-color);
	cursor: pointer;
	color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 0.25rem;
	padding: 0;
	transition: all 0.2s ease;
}

.media-button:hover:not(:disabled) {
	background-color: var(--hover-color);
}

.media-button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.media-button.recording {
	animation: pulse 1.5s infinite;
	border-color: var(--accent-color);
	background-color: var(--hover-color);
}

.send-button {
	width: 32px;
	height: 32px;
	min-width: 32px;
	min-height: 32px;
	border-radius: 50%;
	background-color: var(--text-color);
	border: none;
	cursor: pointer;
	color: var(--bg-color);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 0.25rem;
	padding: 0;
	box-sizing: border-box;
	transition: transform 0.2s ease;
	overflow: hidden;
}

.send-button:hover {
	transform: scale(1.05);
}

.send-button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.send-icon {
	width: 16px;
	height: 16px;
}

.send-controls {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

/* When recording, send controls should take full width */
.send-controls.recording {
	width: 100%;
}

.message {
	display: flex;
	flex-direction: column;
	max-width: 100%;
	margin: 1rem 0;
	padding: 0.5rem 0.75rem;
	border-radius: 0.75rem;
	word-wrap: break-word;
	position: relative;
}

.message.media-only {
	padding: 0;
	margin: 0;
	background: none;
}

.message-user {
	margin-left: auto;
	margin-right: 0;
	background-color: var(--message-bg-user);
	color: var(--text-color);
	width: fit-content;
}

.message-user.media-only {
	margin-left: auto;
	margin-right: 0;
	padding: 0;
	background: none;
}

.message-ai {
	margin-right: 0;
	margin-left: 0;
	background-color: var(--message-bg-ai);
	width: 100%;
	min-height: 48px;
	min-width: 120px;
}

.message-content {
	font-size: 1rem;
	line-height: 1.5;
	min-height: 24px;
}

/* File Upload Styles */
.file-upload {
	display: flex;
	align-items: center;
}

.file-input {
	display: none;
}

.file-button {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 26px;
	height: 26px;
	padding: 4px;
	background: none;
	border: 1px solid var(--accent-color);
	border-radius: 50%;
	cursor: pointer;
	transition: all 0.2s;
	color: var(--accent-color);
}

.file-button:hover {
	background-color: var(--hover-color);
}

.file-icon {
	width: 20px;
	height: 20px;
}

.file-upload.dragging::after {
	content: '';
	position: absolute;
	top: -10px;
	left: -10px;
	right: -10px;
	bottom: -10px;
	border: 2px dashed var(--accent-color);
	border-radius: 8px;
	animation: pulse 1.5s infinite;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}

	50% {
		transform: scale(1.1);
		opacity: 0.5;
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}

/* Message with file attachment styles */
.message-file {
	display: flex;
	align-items: center;
	gap: 0.75rem;
	padding: 0rem;
	background-color: var(--input-bg);
	border: 1px solid var(--border-color);
	border-radius: 0.5rem;
	margin: 0.5rem 0;
	width: 100%;
	max-width: 300px;
}

.message-file-icon {
	width: 32px;
	height: 32px;
	padding: 4px;
	color: inherit;
	background-color: var(--hover-color);
	border-radius: 0.25rem;
	flex-shrink: 0;
}

.message-file-info {
	flex: 1;
	min-width: 0;
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
}

.message-file-name {
	font-weight: 500;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.message-file-name a {
	color: var(--text-color);
	text-decoration: none;
}

.message-file-name a:hover {
	text-decoration: underline;
}

.message-file-size {
	font-size: 0.75rem;
	color: var(--accent-color);
}

/* Markdown content styles */
.message-content> :first-child {
	margin-top: 0;
}

.message-content> :last-child {
	margin-bottom: 0;
}

.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
	margin: 1.5rem 0 1rem;
	line-height: 1.25;
}

.message-content p {
	margin: 0;
}

.message-content ul,
.message-content ol {
	margin: 0.75rem 0;
	padding-left: 1.5rem;
}

.message-content li {
	margin: 0.25rem 0;
}

.message-content pre {
	background-color: var(--input-bg);
	border: 1px solid var(--border-color);
	border-radius: 0.5rem;
	padding: 1rem;
	margin: 0.75rem 0;
	overflow-x: auto;
}

.message-content code {
	font-family: 'SF Mono', Monaco, Menlo, Consolas, 'Ubuntu Mono', monospace;
	font-size: 0.9em;
	padding: 0.2em 0.4em;
	background-color: var(--input-bg);
	border-radius: 0.25rem;
}

.message-content pre code {
	padding: 0;
	background-color: transparent;
}

.message-content blockquote {
	margin: 0.75rem 0;
	padding-left: 1rem;
	border-left: 4px solid var(--border-color);
	color: var(--accent-color);
}

.message-content img {
	max-width: 100%;
	height: auto;
	border-radius: 0.5rem;
	margin: 0.75rem 0;
}

.message-content table {
	width: 100%;
	border-collapse: collapse;
	margin: 0.75rem 0;
}

.message-content th,
.message-content td {
	padding: 0.5rem;
	border: 1px solid var(--border-color);
	text-align: left;
}

.message-content th {
	background-color: var(--input-bg);
}

.message-timestamp {
	font-size: 0.75rem;
	color: #666;
	margin-top: 0.25rem;
	align-self: flex-end;
}

/* Dark mode adjustments for markdown */
@media (prefers-color-scheme: dark) {
	.message-content blockquote {
		color: var(--accent-color);
	}

	.message-content a {
		color: var(--text-color);
	}

	.message-file-size {
		color: var(--accent-color);
	}
}

/* Loading Indicator Styles */
.loading-indicator {
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 8px 0;
	min-height: 24px;
	width: fit-content;
}

.message-ai .loading-indicator {
	margin-left: 0;
}

.dot {
	width: 8px;
	height: 8px;
	background-color: var(--accent-color);
	border-radius: 50%;
	opacity: 0.4;
	animation: loadingDot 1.4s infinite;
}

.dot:nth-child(2) {
	animation-delay: 0.2s;
}

.dot:nth-child(3) {
	animation-delay: 0.4s;
}

@keyframes loadingDot {

	0%,
	100% {
		opacity: 0.4;
		transform: scale(1);
	}

	50% {
		opacity: 1;
		transform: scale(1.1);
	}
}

/* Media Controls Styles */
.media-controls {
	display: flex;
	align-items: center;
	gap: 0.25rem;
	max-width: fit-content;
}

/* When recording, the media controls should expand to full width */
.media-controls.recording {
	max-width: 100%;
	width: 100%;
}

/* Modal Styles */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-overlay.fullscreen {
	background-color: #000;
}

.modal-content {
	background-color: var(--bg-color);
	border-radius: 1rem;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	width: 90%;
	max-width: 600px;
	max-height: 90vh;
	overflow-y: auto;
	position: relative;
}

.modal-content.fullscreen {
	width: 100%;
	height: 100%;
	max-width: none;
	max-height: none;
	border-radius: 0;
	overflow-y: hidden;
	background-color: #000;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 1.5rem;
	border-bottom: 1px solid var(--border-color);
}

.modal-title {
	font-size: 1.5rem;
	font-weight: 600;
	margin: 0;
}

.modal-close {
	background: none;
	border: none;
	padding: 0.5rem;
	cursor: pointer;
	color: var(--accent-color);
	border-radius: 0.5rem;
	transition: background-color 0.2s;
}

.modal-close:hover {
	background-color: var(--hover-color);
}

.modal-body {
	padding: 1.5rem;
}

.modal-body.fullscreen {
	padding: 0;
	height: 100%;
}

/* Introduction Panel Styles */
.introduction-content {
	display: flex;
	flex-direction: column;
	gap: 1.5rem;
}

.intro-section {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

.intro-section h3 {
	font-size: 1.25rem;
	font-weight: 600;
	margin: 0;
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.intro-section ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.intro-section li {
	margin: 0.5rem 0;
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.intro-section li::before {
	content: "•";
	color: var(--accent-color);
}

kbd {
	background-color: var(--input-bg);
	border: 1px solid var(--border-color);
	border-radius: 0.25rem;
	padding: 0.125rem 0.375rem;
	font-size: 0.875rem;
	font-family: monospace;
}

.intro-button {
	background-color: var(--accent-color);
	color: var(--bg-color);
	border: none;
	border-radius: 0.5rem;
	padding: 0.75rem 1.5rem;
	font-size: 1rem;
	font-weight: 600;
	cursor: pointer;
	transition: opacity 0.2s;
	margin-top: 1rem;
	align-self: center;
}

.intro-button:hover {
	opacity: 0.9;
}

/* Update message media styles */
.message-media-container {
	margin: 0.5rem 0;
	border-radius: 0.75rem;
	overflow: hidden;
	max-width: 300px;
	width: 100%;
}

.message-media {
	width: 100%;
	height: auto;
	display: block;
	cursor: pointer;
}

.message.media-only .message-media-container {
	margin: 0;
	border-radius: 0.75rem;
	overflow: hidden;
}

/* Lightbox Styles */
.lightbox-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
	cursor: pointer;
	animation: fadeIn 0.3s ease-out;
}

.lightbox-image {
	max-width: 90vw;
	max-height: 90vh;
	object-fit: contain;
	cursor: default;
	animation: zoomIn 0.3s ease-out;
	will-change: transform;
}

.lightbox-close {
	position: fixed;
	top: 1rem;
	right: 1rem;
	background: rgba(0, 0, 0, 0.5);
	border: none;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	color: white;
	padding: 0.5rem;
	transition: background-color 0.2s, transform 0.2s;
	animation: fadeSlideIn 0.3s ease-out;
}

.lightbox-close:hover {
	background: rgba(0, 0, 0, 0.7);
	transform: scale(1.1);
}

.lightbox-overlay.closing {
	animation: fadeOut 0.2s ease-out forwards;
}

.lightbox-overlay.closing .lightbox-image {
	animation: zoomOut 0.2s ease-out forwards;
}

.lightbox-overlay.closing .lightbox-close {
	animation: fadeSlideOut 0.2s ease-out forwards;
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

@keyframes fadeOut {
	from {
		opacity: 1;
	}

	to {
		opacity: 0;
	}
}

@keyframes zoomIn {
	from {
		opacity: 0;
		transform: scale(0.95);
	}

	to {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes zoomOut {
	from {
		opacity: 1;
		transform: scale(1);
	}

	to {
		opacity: 0;
		transform: scale(0.95);
	}
}

@keyframes fadeSlideIn {
	from {
		opacity: 0;
		transform: translate(20px, -20px);
	}

	to {
		opacity: 1;
		transform: translate(0, 0);
	}
}

@keyframes fadeSlideOut {
	from {
		opacity: 1;
		transform: translate(0, 0);
	}

	to {
		opacity: 0;
		transform: translate(20px, -20px);
	}
}

/* Error Boundary Styles */
.error-boundary {
	padding: 1rem;
	margin: 1rem;
	border: 1px solid var(--border-color);
	border-radius: 0.5rem;
	background-color: var(--input-bg);
}

.error-boundary h2 {
	color: #e53935;
	margin-bottom: 1rem;
}

.error-boundary details {
	margin: 1rem 0;
}

.error-boundary summary {
	cursor: pointer;
	color: var(--accent-color);
}

.error-boundary pre {
	margin-top: 0.5rem;
	padding: 0.5rem;
	background-color: var(--bg-color);
	border-radius: 0.25rem;
	overflow-x: auto;
}

.error-boundary button {
	background-color: var(--accent-color);
	color: var(--bg-color);
	border: none;
	border-radius: 0.25rem;
	padding: 0.5rem 1rem;
	cursor: pointer;
	transition: opacity 0.2s;
}

.error-boundary button:hover {
	opacity: 0.9;
}

/* Audio Recording UI Styles */
.audio-recording-ui {
	display: flex;
	align-items: center;
	gap: 1rem;
	width: 100%;
	flex: 1;
	padding: 0;
	margin: 0;
	background: none;
	border: none;
	height: 32px;
	animation: fadeSlideIn 0.2s ease-out;
}

@keyframes fadeSlideIn {
	from {
		opacity: 0;
		transform: translateY(4px);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.audio-recording-ui.closing {
	animation: fadeSlideOut 0.15s ease-out forwards;
}

@keyframes fadeSlideOut {
	from {
		opacity: 1;
		transform: translateY(0);
	}

	to {
		opacity: 0;
		transform: translateY(4px);
	}
}

.recording-visualization {
	flex: 1;
	display: flex;
	align-items: center;
	gap: 1rem;
	height: 32px;
	animation: expandIn 0.3s ease-out;
	background: transparent;
}

.recording-visualization canvas {
	flex: 1;
	height: 32px;
	border-radius: 0.25rem;
	display: block;
	image-rendering: crisp-edges;
	image-rendering: -webkit-optimize-contrast;
	background: transparent !important;
	background-color: transparent !important;
}

@keyframes expandIn {
	from {
		transform: scaleX(0.97);
		opacity: 0;
	}

	to {
		transform: scaleX(1);
		opacity: 1;
	}
}

.recording-timer {
	font-size: 0.875rem;
	color: var(--accent-color);
	font-variant-numeric: tabular-nums;
	min-width: 40px;
	text-align: right;
}

.recording-control-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32px;
	height: 32px;
	padding: 6px;
	border: none;
	border-radius: 50%;
	background: none;
	cursor: pointer;
	transition: all 0.2s;
	color: var(--accent-color);
	animation: popIn 0.2s ease-out;
}

@keyframes popIn {
	from {
		transform: scale(0.9);
		opacity: 0;
	}

	to {
		transform: scale(1);
		opacity: 1;
	}
}

.recording-control-btn svg {
	stroke-width: 2px;
}

.recording-control-btn.cancel {
	color: var(--text-color);
	background-color: var(--hover-color);
}

.recording-control-btn.confirm {
	color: var(--bg-color);
	background-color: var(--text-color);
}

.recording-control-btn:hover {
	transform: scale(1.05);
}

/* File Menu Styles */
.file-menu {
	position: relative;
	display: flex;
	align-items: center;
}

.file-menu-trigger:hover {
	/* These styles are now handled by the shared selector */
}

.file-menu-dropdown {
	position: absolute;
	bottom: 100%;
	left: 0;
	margin-bottom: 0.5rem;
	background-color: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 0.5rem;
	padding: 0.25rem;
	min-width: 200px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	animation: slideUp 0.2s ease-out;
	transform-origin: bottom left;
}

.file-menu-dropdown.closing {
	animation: slideDown 0.15s ease-out forwards;
	pointer-events: none;
}

@keyframes slideUp {
	from {
		opacity: 0;
		transform: translateY(4px) scale(0.95);
	}

	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

@keyframes slideDown {
	from {
		opacity: 1;
		transform: translateY(0) scale(1);
	}

	to {
		opacity: 0;
		transform: translateY(4px) scale(0.95);
	}
}

.file-menu-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: 0.75rem;
	border: none;
	background: none;
	color: var(--text-color);
	cursor: pointer;
	border-radius: 0.25rem;
	transition: all 0.2s;
	animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateX(-4px);
	}

	to {
		opacity: 1;
		transform: translateX(0);
	}
}

.file-menu-item:not(:last-child) {
	border-bottom: 1px solid var(--border-color);
}

.file-menu-item:hover {
	background-color: var(--hover-color);
}

.file-menu-item span {
	font-size: 0.875rem;
}

.file-menu-item svg {
	width: 20px;
	height: 20px;
	margin-left: 0.5rem;
	color: inherit;
}

/* Camera Modal Styles */
.camera-modal {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
}

.camera-error {
	padding: 0.75rem;
	background-color: rgba(244, 67, 54, 0.1);
	border: 1px solid rgba(244, 67, 54, 0.5);
	border-radius: 0.5rem;
	color: #f44336;
	font-size: 0.875rem;
	text-align: center;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 10;
	max-width: 80%;
}

.camera-preview {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background-color: #000;
	flex-grow: 1;
}

.camera-preview video {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.camera-controls {
	position: absolute;
	bottom: 40px;
	left: 0;
	right: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 1rem 0;
	z-index: 5;
}

.camera-control-btn {
	background: none;
	border: none;
	cursor: pointer;
	color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s;
}

.camera-control-btn.capture {
	width: 70px;
	height: 70px;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.8);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
	padding: 0;
	position: relative;
}

.camera-control-btn.capture svg {
	width: 62px;
	height: 62px;
	color: var(--accent-color);
}

.camera-control-btn.capture::before {
	content: '';
	position: absolute;
	top: -4px;
	left: -4px;
	right: -4px;
	bottom: -4px;
	border: 2px solid rgba(255, 255, 255, 0.5);
	border-radius: 50%;
}

.camera-control-btn:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.send-controls {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.message-input {
	flex: 1;
	min-height: 24px;
	padding: 8px 0;
	margin: 0;
	font-size: 1rem;
	line-height: 1.5;
	color: var(--text-color);
	background: none;
	border: none;
	resize: none;
	outline: none;
	overflow: hidden;
	box-sizing: border-box;
	display: block;
	width: 100%;
	z-index: 1;
}

.message-input::placeholder {
	color: var(--accent-color);
	opacity: 1;
}

/* Animation for the drag overlay */
@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

/* Accessibility - Screen reader only */
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border: 0;
}

/* Custom scrollbar for WebKit browsers (Chrome, Safari) */
::-webkit-scrollbar {
	width: 6px;
	height: 6px;
	background-color: transparent;
}

::-webkit-scrollbar-thumb {
	background-color: rgba(var(--accent-color-rgb), 0.5);
	border-radius: 6px;
}

::-webkit-scrollbar-track {
	background-color: transparent;
}

/* iOS-like scrollbar for message list */
.message-list {
	scrollbar-width: thin;
	scrollbar-color: transparent transparent;
	-webkit-overflow-scrolling: touch;
	scroll-behavior: smooth;
	overflow-x: hidden;
}

/* Auto-hiding scrollbar behavior */
.message-list::-webkit-scrollbar-thumb {
	opacity: 0;
	transition: opacity 0.3s ease;
	background-color: rgba(var(--accent-color-rgb), 0.5);
}

.message-list.scrolling::-webkit-scrollbar-thumb,
.message-list:hover::-webkit-scrollbar-thumb {
	opacity: 1;
}

/* Improve chat container scrolling */
.chat-container {
	-webkit-overflow-scrolling: touch;
	scroll-behavior: smooth;
}

/* Animations for scrollbar */
@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

@keyframes fadeOut {
	from {
		opacity: 1;
	}

	to {
		opacity: 0;
	}
}

/* Welcome Message */
.welcome-message {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	flex: 1;
	padding: 20px;
}

.welcome-card {
	max-width: 500px;
	background-color: var(--message-bg-ai);
	border-radius: 12px;
	padding: 24px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	text-align: center;
	animation: fadeSlideIn 0.5s ease forwards;
}

.welcome-icon {
	width: 64px;
	height: 64px;
	margin: 0 auto 16px;
	color: var(--accent-color);
	opacity: 0.9;
	display: flex;
	align-items: center;
	justify-content: center;
}

.team-profile-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 12px;
	background-color: var(--bg-color);
	border: 2px solid var(--border-color);
}

.welcome-card h2 {
	font-size: 1.6rem;
	font-weight: 600;
	margin-bottom: 12px;
	color: var(--text-color);
}

.welcome-card p {
	font-size: 1.1rem;
	margin-bottom: 16px;
	color: var(--text-secondary);
	line-height: 1.5;
}

.welcome-actions {
	text-align: left;
	border-top: 1px solid rgba(0, 0, 0, 0.08);
	padding-top: 16px;
	margin-top: 8px;
}

.welcome-actions p {
	font-weight: 500;
	margin-bottom: 16px;
	font-size: 1.1rem;
	text-align: center;
}

.welcome-buttons {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.welcome-action-button {
	background-color: var(--bg-color);
	color: var(--text-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	padding: 12px 16px;
	font-size: 1rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s ease;
	width: 100%;
	text-align: center;
}

.welcome-action-button.primary {
	background-color: var(--text-color);
	color: var(--bg-color);
	border-color: var(--text-color);
}

.welcome-action-button:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.welcome-action-button.primary:hover {
	background-color: var(--accent-color);
	border-color: var(--accent-color);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
	.welcome-card {
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.25);
	}

	.welcome-actions {
		border-top-color: rgba(255, 255, 255, 0.08);
	}
}

/* Skeleton Loader */
.skeleton-loader {
	background: linear-gradient(90deg,
			var(--skeleton-start) 0%,
			var(--skeleton-end) 50%,
			var(--skeleton-start) 100%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s ease-in-out infinite;
	opacity: 0.7;
}

@keyframes skeleton-loading {
	0% {
		background-position: 0% 0;
	}

	100% {
		background-position: -200% 0;
	}
}

/* Lazy-loading skeleton containers */
.lazy-skeleton-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0.5rem;
}

.mediacontrols-skeleton {
	height: 40px;
	width: 40px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.filemenu-skeleton {
	position: relative;
	display: inline-flex;
}

.modal-skeleton,
.lightbox-skeleton,
.cameramodal-skeleton {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.5);
}

/* Standalone toggle button for widget mode */
.chat-toggle.standalone {
	position: fixed;
	top: auto;
	bottom: 20px;
	right: 20px;
	width: 60px;
	height: 60px;
	border-radius: 50%;
	background-color: #000000;
	color: #ffffff;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	z-index: 1001;
	transition: all 0.3s ease;
	padding: 0;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
	overflow: hidden;
}

/* Different styling for when chat is open vs closed */
.chat-toggle.standalone.chat-open {
	background-color: #ffffff;
	color: #000000;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.chat-toggle.standalone:hover {
	transform: scale(1.05);
	box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
}

.chat-toggle.standalone svg {
	width: 32px;
	height: 32px;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
	.chat-toggle.standalone {
		background-color: #ffffff;
		color: #000000;
	}

	.chat-toggle.standalone.chat-open {
		background-color: #000000;
		color: #ffffff;
	}
}

/* Mobile adjustments for standalone toggle */
@media (max-width: 768px) {
	.chat-toggle.standalone {
		bottom: 20px;
		/* Already at bottom, just maintaining for clarity */
	}
}

.textarea-wrapper {
	width: 100%;
	position: relative;
	display: block;
	flex: 1;
}

/* Scheduling Components Styles */

/* Schedule Button */
.schedule-button {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: transparent;
	border: 1px solid var(--accent-color);
	border-radius: 20px;
	padding: 6px 12px;
	height: 32px;
	cursor: pointer;
	color: var(--accent-color);
	margin-left: 0.5rem;
	transition: all 0.2s ease;
}

.schedule-button:hover {
	background-color: var(--hover-color);
}

.schedule-button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.calendar-icon {
	width: 16px;
	height: 16px;
	margin-right: 6px;
}

.schedule-button-text {
	font-size: 14px;
	font-weight: 500;
}

/* Date Selector */
.date-selector {
	background-color: var(--message-bg-ai);
	border-radius: 8px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	max-width: 320px;
	animation: fadeSlideIn 0.3s ease forwards;
}

.date-selector-title {
	margin-bottom: 12px;
	text-align: center;
}

.date-selector-title h3 {
	font-size: 16px;
	font-weight: 600;
	color: var(--text-color);
}

.date-grid {
	display: flex;
	flex-direction: column;
	gap: 8px;
	margin-bottom: 16px;
}

.date-row {
	display: flex;
	gap: 8px;
	justify-content: space-between;
}

.date-button {
	flex: 1;
	background-color: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	padding: 10px 0;
	cursor: pointer;
	transition: all 0.2s ease;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-width: 70px;
}

.date-button:hover,
.date-button.hovered {
	background-color: var(--hover-color);
	border-color: var(--accent-color);
	transform: translateY(-2px);
}

.date-button.today {
	border-color: var(--accent-color);
	background-color: rgba(var(--accent-color-rgb), 0.1);
}

.date-button-text {
	font-weight: 500;
	font-size: 14px;
}

.today-indicator {
	font-size: 10px;
	color: var(--accent-color);
	margin-top: 4px;
}

.date-selector-footer {
	display: flex;
	justify-content: center;
}

.more-dates-button {
	background-color: transparent;
	border: 1px solid var(--border-color);
	color: var(--accent-color);
	cursor: pointer;
	font-size: 14px;
	padding: 8px 16px;
	border-radius: 16px;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	margin-top: 8px;
}

.more-dates-button:hover {
	background-color: var(--hover-color);
	text-decoration: underline;
	border-color: var(--accent-color);
}

.more-dates-icon {
	margin-right: 6px;
}

/* Time of Day Selector */
.time-of-day-selector {
	background-color: var(--message-bg-ai);
	border-radius: 8px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	max-width: 320px;
	animation: fadeSlideIn 0.3s ease forwards;
}

.time-selector-title {
	margin-bottom: 12px;
	text-align: center;
}

.time-selector-title h3 {
	font-size: 16px;
	font-weight: 600;
	color: var(--text-color);
}

.time-of-day-options {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.time-of-day-button {
	display: flex;
	align-items: center;
	background-color: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	padding: 12px;
	cursor: pointer;
	transition: all 0.2s ease;
	width: 100%;
	text-align: left;
}

.time-of-day-button:hover,
.time-of-day-button.hovered {
	background-color: var(--hover-color);
	border-color: var(--accent-color);
	transform: translateY(-2px);
}

.time-of-day-icon {
	margin-right: 12px;
	color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
}

.time-of-day-info {
	flex: 1;
}

.time-of-day-label {
	font-weight: 600;
	font-size: 14px;
	margin-bottom: 4px;
}

.time-of-day-description {
	font-size: 12px;
	color: var(--accent-color);
}

/* Time Slot Selector */
.time-slot-selector {
	background-color: var(--message-bg-ai);
	border-radius: 8px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	max-width: 320px;
	animation: fadeSlideIn 0.3s ease forwards;
}

.time-slot-title {
	margin-bottom: 16px;
	text-align: center;
}

.time-slot-title h3 {
	font-size: 16px;
	font-weight: 600;
	color: var(--text-color);
	margin-bottom: 4px;
}

.time-slot-subtitle {
	font-size: 14px;
	color: var(--accent-color);
}

.time-slots-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 8px;
	max-height: 300px;
	overflow-y: auto;
	padding-right: 4px;
}

.time-slots-grid::-webkit-scrollbar {
	width: 6px;
}

.time-slots-grid::-webkit-scrollbar-thumb {
	background-color: var(--border-color);
	border-radius: 3px;
}

.time-slots-grid::-webkit-scrollbar-track {
	background-color: transparent;
}

.time-slot-button {
	background-color: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	padding: 10px;
	cursor: pointer;
	transition: all 0.2s ease;
	font-size: 14px;
	font-weight: 500;
	text-align: center;
}

.time-slot-button:hover,
.time-slot-button.hovered {
	background-color: var(--hover-color);
	border-color: var(--accent-color);
	transform: translateY(-2px);
}

/* Schedule Confirmation */
.schedule-confirmation {
	display: flex;
	align-items: center;
	background-color: rgba(var(--accent-color-rgb), 0.1);
	border: 1px solid var(--border-color);
	border-radius: 8px;
	padding: 12px;
	margin: 8px 0;
	animation: fadeIn 0.3s ease forwards;
}

.schedule-confirmation-icon {
	margin-right: 12px;
	color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
}

.confirmation-calendar-icon {
	color: var(--accent-color);
}

.schedule-confirmation-details {
	flex: 1;
}

.schedule-confirmation-title {
	font-weight: 600;
	font-size: 14px;
	margin-bottom: 4px;
	display: flex;
	align-items: center;
}

.schedule-confirmation-date {
	font-size: 14px;
	margin-bottom: 2px;
}

.schedule-confirmation-time {
	font-size: 14px;
	color: var(--accent-color);
}

/* Scheduling Container */
.scheduling-container {
	margin: 12px 0;
	animation: fadeIn 0.3s ease forwards;
}

/* Service selection components */
.service-selection-container {
	margin: 12px 0;
	animation: fadeIn 0.3s ease forwards;
}

.service-buttons {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	max-width: 400px;
}

.matter-canvas {
	margin: 8px 0;
	border: 1px solid var(--border-color);
	border-radius: 8px;
	background: var(--bg-color);
	overflow: hidden;
}

.matter-canvas-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 8px 12px;
	background: rgba(var(--accent-color-rgb), 0.05);
	border-bottom: 1px solid var(--border-color);
}

.matter-canvas-title {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 14px;
	font-weight: 500;
	color: var(--text-color);
}

.matter-canvas-toggle {
	background: none;
	border: none;
	padding: 4px;
	cursor: pointer;
	color: var(--text-color);
	opacity: 0.7;
	transition: opacity 0.2s ease;
}

.matter-canvas-toggle:hover {
	opacity: 1;
}

.matter-canvas-content {
	padding: 0;
}

.matter-canvas-markdown {
	margin: 0;
	padding: 12px;
	font-family: ui-monospace, 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
	font-size: 13px;
	line-height: 1.5;
	color: var(--text-color);
	background: transparent;
	border: none;
	white-space: pre-wrap;
	word-wrap: break-word;
	overflow-x: auto;
}

/* Feedback UI Styles */
.feedback-ui {
	margin-top: 12px;
	padding: 8px 0;
	border-top: 1px solid var(--border-color);
	opacity: 0.8;
	transition: opacity 0.2s ease;
}

.feedback-ui:hover {
	opacity: 1;
}

.feedback-ui.submitted {
	opacity: 0.6;
}

.feedback-actions {
	display: flex;
	align-items: center;
	gap: 16px;
	flex-wrap: wrap;
}

.feedback-thumbs {
	display: flex;
	gap: 8px;
}

.feedback-btn {
	background: none;
	border: 1px solid var(--border-color);
	border-radius: 6px;
	padding: 6px 8px;
	cursor: pointer;
	transition: all 0.2s ease;
	color: var(--text-color);
	opacity: 0.7;
}

.feedback-btn:hover {
	opacity: 1;
	background: var(--hover-color);
}

.feedback-btn.active {
	opacity: 1;
	background: var(--accent-color);
	color: var(--bg-color);
	border-color: var(--accent-color);
}

.feedback-btn svg {
	width: 16px;
	height: 16px;
}

.feedback-rating {
	display: flex;
	gap: 4px;
}

.feedback-star {
	background: none;
	border: none;
	cursor: pointer;
	padding: 4px;
	color: var(--border-color);
	transition: color 0.2s ease;
}

.feedback-star:hover,
.feedback-star.active {
	color: #fbbf24;
}

.feedback-star svg {
	width: 14px;
	height: 14px;
	fill: currentColor;
}

.feedback-expand-btn {
	background: none;
	border: 1px solid var(--border-color);
	border-radius: 6px;
	padding: 6px 8px;
	cursor: pointer;
	transition: all 0.2s ease;
	color: var(--text-color);
	opacity: 0.7;
}

.feedback-expand-btn:hover {
	opacity: 1;
	background: var(--hover-color);
}

.feedback-expand-btn svg {
	width: 16px;
	height: 16px;
}

.feedback-expanded {
	margin-top: 12px;
	animation: fadeIn 0.2s ease;
}

.feedback-comment {
	width: 100%;
	min-height: 60px;
	padding: 8px 12px;
	border: 1px solid var(--border-color);
	border-radius: 6px;
	background: var(--input-bg);
	color: var(--text-color);
	font-family: inherit;
	font-size: 14px;
	line-height: 1.4;
	resize: vertical;
	transition: border-color 0.2s ease;
}

.feedback-comment:focus {
	outline: none;
	border-color: var(--accent-color);
}

.feedback-comment::placeholder {
	color: var(--accent-color);
	opacity: 0.6;
}

.feedback-submit-actions {
	display: flex;
	gap: 8px;
	margin-top: 8px;
	justify-content: flex-end;
}

.feedback-submit-btn {
	background: var(--accent-color);
	color: var(--bg-color);
	border: none;
	border-radius: 6px;
	padding: 8px 16px;
	cursor: pointer;
	font-size: 14px;
	transition: all 0.2s ease;
}

.feedback-submit-btn:hover:not(:disabled) {
	opacity: 0.9;
	transform: translateY(-1px);
}

.feedback-submit-btn:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.feedback-cancel-btn {
	background: none;
	color: var(--accent-color);
	border: 1px solid var(--border-color);
	border-radius: 6px;
	padding: 8px 16px;
	cursor: pointer;
	font-size: 14px;
	transition: all 0.2s ease;
}

.feedback-cancel-btn:hover {
	background: var(--hover-color);
}

.feedback-thanks {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 14px;
	color: var(--accent-color);
	opacity: 0.8;
}

.feedback-thanks svg {
	width: 16px;
	height: 16px;
	color: #10b981;
}

/* Hide feedback UI on user messages */
.message-user .feedback-ui {
	display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	.feedback-actions {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.feedback-submit-actions {
		flex-direction: column;
		width: 100%;
	}

	.feedback-submit-btn,
	.feedback-cancel-btn {
		width: 100%;
		justify-content: center;
	}
}