[{"id": "demo", "name": "Blawby Demo", "config": {"aiModel": "llama", "consultationFee": 0, "requiresPayment": false, "ownerEmail": "paul<PERSON><PERSON><PERSON><EMAIL>", "availableServices": ["general-consultation", "legal-advice"], "serviceQuestions": {"general-consultation": ["Thanks for reaching out! I'd love to help. Can you tell me what legal situation you're dealing with?", "Have you been able to take any steps to address this yet? No worries if you're just getting started - that's what we're here for.", "What would a good outcome look like for you? Sometimes it helps to think about what you're hoping for.", "Do you happen to have any documents or information that might be relevant? Don't worry if you don't have everything - we can work with whatever you have."], "legal-advice": ["What type of legal advice are you seeking?", "What is the timeline or urgency of your situation?", "Have you consulted with any other legal professionals about this?", "What specific questions do you have about your legal rights or options?"]}, "domain": "demo.blawby.com", "description": "Demo law firm for testing purposes", "paymentLink": null, "brandColor": "#2563eb", "accentColor": "#3b82f6", "introMessage": "Hello! I'm your AI legal assistant. I'm here to help you get started with your legal questions and can assist with general consultation and legal advice. How can I help you today?", "profileImage": "/team-profile-demo.png", "webhooks": {"enabled": true, "url": "https://webhook.site/1b8e8c8e-2aa5-480d-8bb1-0ebe3ae40503", "secret": "demo-webhook-secret-2025", "events": {"matterCreation": true, "matterDetails": true, "contactForm": true, "appointment": true}, "retryConfig": {"maxRetries": 3, "retryDelay": 60}}}}, {"id": "north-carolina-legal-services", "name": "North Carolina Legal Services", "config": {"aiModel": "llama", "consultationFee": 75, "requiresPayment": true, "ownerEmail": "paul<PERSON><PERSON><PERSON><EMAIL>", "availableServices": ["Family Law", "Small Business and Nonprofits", "Employment Law", "Tenant Rights Law", "Probate and Estate Planning", "Special Education and IEP Advocacy"], "serviceQuestions": {"Family Law": ["Thanks for reaching out. I know family situations can be really difficult. Can you tell me what type of family issue you're going through? (For example, divorce, custody, child support...)", "Are there any children involved? No worries if this is complicated to explain - just let me know what you're comfortable sharing.", "Have you and your spouse or partner made any formal agreements yet, or received any court orders? It's okay if you haven't - we're just trying to understand where things stand.", "Can you tell me a bit about your current living situation? I know this might be in flux right now.", "Do you currently have any court orders or formal agreements in place? Again, no judgment if things are still being worked out."], "Small Business and Nonprofits": ["What type of business entity are you operating or planning to start?", "What specific legal issue are you facing with your business?", "Are you dealing with contracts, employment issues, or regulatory compliance?", "What is the size and scope of your business operations?", "Do you have any existing business documents or agreements?"], "Employment Law": ["I'm sorry you're dealing with workplace issues - that can be really stressful. Can you tell me what's been happening at work? (For example, discrimination, harassment, wage problems...)", "Are you currently working there, or have you recently left or been let go? No judgment either way - just trying to understand your situation.", "Have you been able to talk to anyone at your company about this, like <PERSON><PERSON> or a manager? Or have you reached out to any agencies? It's totally fine if you haven't yet.", "Do you have any documentation about what happened - emails, texts, photos, anything like that? Even small details can be helpful.", "How long has this been going on? I know these situations can build up over time."], "Tenant Rights Law": ["What specific tenant rights issue are you facing? (eviction, repairs, security deposit, etc.)", "Are you currently renting or have you recently moved out?", "Have you received any notices from your landlord?", "What is the condition of your rental property?", "Have you documented any issues with photos or written communication?"], "Probate and Estate Planning": ["Are you dealing with probate of an estate or planning your own estate?", "What is the approximate value of the estate or assets involved?", "Do you have a will or other estate planning documents?", "Are there any disputes or conflicts among family members?", "What specific questions do you have about the probate process or estate planning?"], "Special Education and IEP Advocacy": ["What grade level is your child in and what type of school do they attend?", "What specific learning challenges or disabilities does your child have?", "Do you have an existing IEP or 504 plan?", "What specific issues are you having with the school or district?", "Have you had any recent meetings with school staff about your child's needs?"]}, "domain": "northcarolinalegalservices.blawby.com", "description": "Affordable, comprehensive legal services for North Carolina. Family Law, Small Business, Employment, Tenant Rights, Probate, Special Education, and more.", "paymentLink": "https://app.blawby.com/northcarolinalegalservices/pay?amount=7500", "brandColor": "#059669", "accentColor": "#10b981", "introMessage": "Welcome to North Carolina Legal Services! I'm here to help you with affordable legal assistance in areas including Family Law, Small Business, Employment, Tenant Rights, Probate, and Special Education. I can answer your questions and help you schedule a consultation with our experienced attorneys. How can I assist you today?", "profileImage": null, "webhooks": {"enabled": true, "url": "https://webhook.site/north-carolina-legal-services-webhook", "secret": "ncls-webhook-secret-2025", "events": {"matterCreation": true, "matterDetails": true, "contactForm": true, "appointment": true}, "retryConfig": {"maxRetries": 3, "retryDelay": 60}}}}]