# Cloudflare Configuration
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token_here
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id_here

# Worker Configuration
WORKER_NAME=blawby-ai-chatbot
WORKER_ENVIRONMENT=production

# Database Configuration
D1_DATABASE_NAME=blawby-ai-chatbot
D1_DATABASE_ID=your_d1_database_id_here

# KV Storage Configuration
KV_NAMESPACE_ID=your_kv_namespace_id_here

# Email Configuration (Resend)
RESEND_API_KEY=your_resend_api_key_here
RESEND_FROM_EMAIL=<EMAIL>

# Frontend Configuration
VITE_API_BASE_URL=https://blawby-ai-chatbot.paulchrisluke.workers.dev
VITE_APP_NAME=Blawby AI Chatbot
VITE_APP_VERSION=1.0.0

# Development Configuration
NODE_ENV=development
VITE_DEV_MODE=true

# Cloudflare Pages Configuration (for GitHub Actions)
CLOUDFLARE_PAGES_PROJECT_NAME=your_pages_project_name_here

# Optional: Custom Domain Configuration
CUSTOM_DOMAIN=ai.blawby.com
ZONE_ID=your_zone_id_here

# Optional: AI Model Configuration
AI_MODEL=llama-3.1-8b-instant
AI_PROVIDER=cloudflare

# Optional: Security Configuration
CORS_ORIGIN=*
SESSION_SECRET=your_session_secret_here

# Optional: Monitoring and Analytics
SENTRY_DSN=your_sentry_dsn_here
ANALYTICS_ID=your_analytics_id_here 